@extends('layouts.modernized-admin')

@section('title', 'Deposit Details - Vertigo AMS')

@section('page-title', 'Deposit Details')
@section('page-subtitle', 'Detailed information about the deposit transaction.')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('reports.modernized.deposits') }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Back to Deposits Report</span>
        <span class="lg:hidden">Back</span>
    </a>
    <button onclick="window.print()" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
        </svg>
        <span class="hidden lg:inline">Print Details</span>
        <span class="lg:hidden">Print</span>
    </button>
</div>
@endsection

@section('content')
<!-- Transaction Information Card -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200 bg-green-50">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-full mr-4">
                <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-gray-900">Deposit Transaction #{{ $transaction->id }}</h3>
                <p class="text-sm text-gray-600 mt-1">Customer deposit details</p>
            </div>
        </div>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Customer Information -->
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Customer Information</h4>
                <div class="space-y-4">
                    @if($transaction->user)
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-12 w-12">
                            <div class="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                                <svg class="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-lg font-medium text-gray-900">{{ $transaction->user->name }}</div>
                            <div class="text-sm text-gray-500">{{ $transaction->user->email }}</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Phone</label>
                            <div class="mt-1 text-sm text-gray-900">{{ $transaction->user->phone ?? 'N/A' }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Customer ID</label>
                            <div class="mt-1 text-sm text-gray-900">#{{ $transaction->user->id }}</div>
                        </div>
                    </div>
                    @else
                    <div class="text-sm text-gray-500">No customer information available</div>
                    @endif
                </div>
            </div>

            <!-- Deposit Information -->
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Deposit Information</h4>
                <div class="space-y-4">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-green-800">Deposit Amount</div>
                        <div class="text-2xl font-bold text-green-900">+{{ _money($transaction->amount) }}</div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Transaction Date</label>
                            <div class="mt-1 text-sm text-gray-900">{{ $transaction->created_at->format('M d, Y H:i') }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Reference Number</label>
                            <div class="mt-1 text-sm text-gray-900">{{ $transaction->reference_number ?? 'N/A' }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Transaction Code</label>
                            <div class="mt-1 text-sm text-gray-900">{{ $transaction->code ?? 'N/A' }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <div class="mt-1">
                                @if($transaction->closed_by)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Processed
                                </span>
                                @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Pending
                                </span>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($transaction->description)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Deposit Notes</label>
                        <div class="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{{ $transaction->description }}</div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Information -->
@if($transaction->account)
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Account Information</h3>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Account Details</h4>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Account Name</label>
                        <div class="mt-1 text-sm text-gray-900">{{ $transaction->account->account_name ?? 'N/A' }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Account Number</label>
                        <div class="mt-1 text-sm text-gray-900">{{ $transaction->account->account_number ?? 'N/A' }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Account Type</label>
                        <div class="mt-1 text-sm text-gray-900">{{ $transaction->account->account_type ?? 'N/A' }}</div>
                    </div>
                </div>
            </div>

            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Account Balance</h4>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-sm font-medium text-blue-800">Current Balance</div>
                    <div class="text-xl font-bold text-blue-900">{{ _money($transaction->account->amount) }}</div>
                </div>
                
                @if($transaction->account->user)
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">Account Holder</label>
                    <div class="mt-1 text-sm text-gray-900">{{ $transaction->account->user->name }}</div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endif

<!-- Related Auction -->
@if($transaction->auction)
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Related Auction</h3>
    </div>

    <div class="p-6">
        <div class="flex items-start space-x-6">
            <!-- Auction Item Image -->
            @if($transaction->auction->item && $transaction->auction->item->getFirstMediaUrl('images'))
            <div class="flex-shrink-0">
                <img class="h-24 w-24 rounded-lg object-cover" src="{{ $transaction->auction->item->getFirstMediaUrl('images') }}" alt="{{ $transaction->auction->item->name }}">
            </div>
            @endif

            <!-- Auction Details -->
            <div class="flex-1">
                <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ $transaction->auction->item->name ?? 'Auction Item' }}</h4>
                @if($transaction->auction->description)
                <p class="text-gray-600 mb-4">{{ $transaction->auction->description }}</p>
                @endif

                <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Auction ID</label>
                        <div class="mt-1 text-sm text-gray-900">#{{ $transaction->auction->id }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Bid Amount</label>
                        <div class="mt-1 text-sm text-gray-900">{{ _money($transaction->auction->bid_amount) }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Auction Code</label>
                        <div class="mt-1 text-sm text-gray-900">{{ $transaction->auction->code ?? 'N/A' }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date</label>
                        <div class="mt-1 text-sm text-gray-900">{{ $transaction->auction->created_at->format('M d, Y') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Processing Information -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Processing Information</h3>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Created By</h4>
                @if($transaction->createdBy)
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <svg class="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">{{ $transaction->createdBy->name }}</div>
                        <div class="text-sm text-gray-500">{{ $transaction->createdBy->email }}</div>
                    </div>
                </div>
                @else
                <div class="text-sm text-gray-500">No staff information available</div>
                @endif
            </div>

            @if($transaction->closed_by)
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Processed By</h4>
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                            <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">Staff ID: {{ $transaction->closed_by }}</div>
                        <div class="text-sm text-gray-500">Deposit processed</div>
                    </div>
                </div>
            </div>
            @else
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Processing Status</h4>
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                            <svg class="h-5 w-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">Pending Processing</div>
                        <div class="text-sm text-gray-500">Awaiting staff approval</div>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <div class="mt-6 pt-6 border-t border-gray-200">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Deposit Date</label>
                    <div class="mt-1 text-sm text-gray-900">{{ $transaction->created_at->format('M d, Y H:i') }}</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                    <div class="mt-1 text-sm text-gray-900">{{ $transaction->updated_at->format('M d, Y H:i') }}</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
