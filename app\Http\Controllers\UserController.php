<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\User;
use App\Models\Status;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\UserStoreRequest;
use App\Http\Requests\UserUpdateRequest;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class UserController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', User::class);

        $users = User::search($request->search)->latest()
                ->when(request()->role_id, function($q){
                    $q->whereHas("roles", function($q){
                        $q->where('id', request()->role_id);
                    });
                })->get();

        return view('app.users.index', compact('users'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', User::class);

        $roles = Role::get();
        $statuses = Status::whereIn("id", [1,2])->pluck("name", 'id');
        return view('app.users.create', compact('roles','statuses'));
    }

    /**
     * @param \App\Http\Requests\UserStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(UserStoreRequest $request)
    {
        $this->authorize('create', User::class);

        $validated = $request->validated();

        $validated['password'] = Hash::make($validated['password']);

        $user = User::create($validated);

        $user->syncRoles($request->roles);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $user->addMedia($file)->toMediaCollection("media");
            }
        }

        return redirect()
            ->route('users.edit', $user)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, User $user)
    {
        $this->authorize('view', $user);

        return view('app.users.show', compact('user'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, User $user)
    {
        $this->authorize('update', $user);

        $roles = Role::get();
        $statuses = Status::whereIn("id", [1,2])->pluck("name", 'id');

        return view('app.users.edit', compact('user', 'roles', 'statuses'));
    }

    /**
     * @param \App\Http\Requests\UserUpdateRequest $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function update(UserUpdateRequest $request, User $user)
    {
        $this->authorize('update', $user);

        $validated = $request->validated();

        if (empty($validated['password'])) {
            unset($validated['password']);
        } else {
            $validated['password'] = Hash::make($validated['password']);
        }
        $user->update($validated);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $user->addMedia($file)->toMediaCollection("media");
            }
        }

        $user->syncRoles($request->roles);

        return redirect()
            ->route('users.edit', $user)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, User $user)
    {
        $this->authorize('delete', $user);

        $user->delete();

        return redirect()
            ->route('users.index')
            ->withSuccess(__('crud.common.removed'));
    }

    // Modernized Admin Methods

    /**
     * Modernized admin index view for users
     */
    public function modernizedIndex(Request $request)
    {
        $this->authorize('view-any', User::class);

        $users = User::search($request->search)->latest()
                ->when(request()->role_id, function($q){
                    $q->whereHas("roles", function($q){
                        $q->where('id', request()->role_id);
                    });
                })->get();

        return view('admin.modernized-users.index', compact('users'));
    }

    /**
     * Modernized admin create view for users
     */
    public function modernizedCreate(Request $request)
    {
        $this->authorize('create', User::class);

        $roles = Role::get();
        $statuses = Status::whereIn("id", [1,2])->pluck("name", 'id');
        return view('admin.modernized-users.create', compact('roles','statuses'));
    }

    /**
     * Modernized admin store method for users
     */
    public function modernizedStore(UserStoreRequest $request)
    {
        $this->authorize('create', User::class);

        $validated = $request->validated();

        $validated['password'] = Hash::make($validated['password']);

        $user = User::create($validated);

        $user->syncRoles($request->roles);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $user->addMedia($file)->toMediaCollection("media");
            }
        }

        return redirect()
            ->route('admin.modernized.users.edit', $user)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Modernized admin show view for users
     */
    public function modernizedShow(Request $request, User $user)
    {
        $this->authorize('view', $user);

        return view('admin.modernized-users.show', compact('user'));
    }

    /**
     * Modernized admin edit view for users
     */
    public function modernizedEdit(Request $request, User $user)
    {
        $this->authorize('update', $user);

        $roles = Role::get();
        $statuses = Status::whereIn("id", [1,2])->pluck("name", 'id');

        return view('admin.modernized-users.edit', compact('user', 'roles', 'statuses'));
    }

    /**
     * Modernized admin update method for users
     */
    public function modernizedUpdate(UserUpdateRequest $request, User $user)
    {
        $this->authorize('update', $user);

        $validated = $request->validated();

        if (empty($validated['password'])) {
            unset($validated['password']);
        } else {
            $validated['password'] = Hash::make($validated['password']);
        }
        $user->update($validated);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $user->addMedia($file)->toMediaCollection("media");
            }
        }

        $user->syncRoles($request->roles);

        return redirect()
            ->route('admin.modernized.users.edit', $user)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Modernized admin destroy method for users
     */
    public function modernizedDestroy(User $user)
    {
        $this->authorize('delete', $user);

        $user->delete();

        return redirect()
            ->route('admin.modernized.users.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function deleteMedia(Request $request, Media $media) {
        $media->delete();
        return response("Success");
    }    

    public function deleteMediaGet(Request $request, Media $media) {
        $media->delete();
        return redirect()->back()->withSuccess('Success');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function profile()
    {
        return view('frontend.profile');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function saveProfile(Request $request)
    {
        $params = request()->all();
        $user = auth()->user(); 
        if (empty($params['password'])) {
            unset($params['password']);
        } else {
            $params['password'] = Hash::make($params['password']);
        }

        $user->update($params);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $user->addMedia($file)->toMediaCollection("media");
            }
        }
        return redirect()->back()->withSuccess(__('crud.common.saved'));
    }

    public function setBranch(Request $request, Branch $branch) {
        auth()->user()->update(['branch_id' => $branch->id]);
        return redirect()->back()->withSuccess("Branch set Successfully");
    }





}
