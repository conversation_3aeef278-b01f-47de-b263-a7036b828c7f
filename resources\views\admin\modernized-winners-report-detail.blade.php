@extends('layouts.modernized-admin')

@section('title', 'Winner Details - Vertigo AMS')

@section('page-title', 'Winner Details')
@section('page-subtitle', 'Detailed information about the winning bid and auction.')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('reports.modernized.winners') }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Back to Winners Report</span>
        <span class="lg:hidden">Back</span>
    </a>
    <button onclick="window.print()" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
        </svg>
        <span class="hidden lg:inline">Print Details</span>
        <span class="lg:hidden">Print</span>
    </button>
</div>
@endsection

@section('content')
<!-- Winner Information Card -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200 bg-green-50">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-full mr-4">
                <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-gray-900">Winning Bid #{{ $auction->id }}</h3>
                <p class="text-sm text-gray-600 mt-1">Auction completed successfully</p>
            </div>
        </div>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Winner Information -->
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Winner Information</h4>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-12 w-12">
                            <div class="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                                <svg class="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-lg font-medium text-gray-900">{{ $auction->user->name ?? 'Unknown Winner' }}</div>
                            <div class="text-sm text-gray-500">{{ $auction->user->email ?? 'N/A' }}</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Phone</label>
                            <div class="mt-1 text-sm text-gray-900">{{ $auction->user->phone ?? 'N/A' }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">User ID</label>
                            <div class="mt-1 text-sm text-gray-900">#{{ $auction->user->id ?? 'N/A' }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bid Information -->
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Bid Information</h4>
                <div class="space-y-4">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-green-800">Winning Bid Amount</div>
                        <div class="text-2xl font-bold text-green-900">{{ _money($auction->bid_amount) }}</div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Bid Date</label>
                            <div class="mt-1 text-sm text-gray-900">{{ $auction->created_at->format('M d, Y H:i') }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Auction Code</label>
                            <div class="mt-1 text-sm text-gray-900">{{ $auction->code ?? 'N/A' }}</div>
                        </div>
                    </div>

                    @if($auction->description)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Notes</label>
                        <div class="mt-1 text-sm text-gray-900">{{ $auction->description }}</div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Item Information Card -->
@if($auction->item)
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Item Details</h3>
    </div>

    <div class="p-6">
        <div class="flex items-start space-x-6">
            <!-- Item Image -->
            <div class="flex-shrink-0">
                @if($auction->item->getFirstMediaUrl('images'))
                <img class="h-32 w-32 rounded-lg object-cover" src="{{ $auction->item->getFirstMediaUrl('images') }}" alt="{{ $auction->item->name }}">
                @else
                <div class="h-32 w-32 rounded-lg bg-gray-200 flex items-center justify-center">
                    <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                @endif
            </div>

            <!-- Item Details -->
            <div class="flex-1">
                <h4 class="text-xl font-semibold text-gray-900 mb-2">{{ $auction->item->name }}</h4>
                @if($auction->item->description)
                <p class="text-gray-600 mb-4">{{ $auction->item->description }}</p>
                @endif

                <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Item Code</label>
                        <div class="mt-1 text-sm text-gray-900">{{ $auction->item->code ?? 'N/A' }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Bid Amount</label>
                        <div class="mt-1 text-sm text-gray-900">{{ _money($auction->item->bid_amount) }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Target Amount</label>
                        <div class="mt-1 text-sm text-gray-900">{{ _money($auction->item->target_amount) }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Status</label>
                        <div class="mt-1">
                            @if($auction->item->closed_by)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Sold
                            </span>
                            @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Available
                            </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Auction Type Information -->
@if($auction->auctionType)
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Auction Information</h3>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ $auction->auctionType->name }}</h4>
                @if($auction->auctionType->description)
                <p class="text-gray-600 mb-4">{{ $auction->auctionType->description }}</p>
                @endif

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Auction Type</label>
                        <div class="mt-1 text-sm text-gray-900">{{ ucfirst($auction->auctionType->type ?? 'N/A') }}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Starting Bid</label>
                        <div class="mt-1 text-sm text-gray-900">{{ _money($auction->auctionType->bid_amount) }}</div>
                    </div>
                </div>
            </div>

            @if($auction->auctionType->date_from || $auction->auctionType->date_to)
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Auction Period</h4>
                <div class="grid grid-cols-2 gap-4">
                    @if($auction->auctionType->date_from)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Start Date</label>
                        <div class="mt-1 text-sm text-gray-900">{{ \Carbon\Carbon::parse($auction->auctionType->date_from)->format('M d, Y H:i') }}</div>
                    </div>
                    @endif
                    @if($auction->auctionType->date_to)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">End Date</label>
                        <div class="mt-1 text-sm text-gray-900">{{ \Carbon\Carbon::parse($auction->auctionType->date_to)->format('M d, Y H:i') }}</div>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endif

<!-- Transaction Information -->
@if($auction->transaction)
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Payment Information</h3>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700">Transaction ID</label>
                <div class="mt-1 text-sm text-gray-900">#{{ $auction->transaction->id }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Amount</label>
                <div class="mt-1 text-sm font-semibold text-green-600">{{ _money($auction->transaction->amount) }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Reference</label>
                <div class="mt-1 text-sm text-gray-900">{{ $auction->transaction->reference_number ?? 'N/A' }}</div>
            </div>
        </div>

        @if($auction->transaction->description)
        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700">Transaction Notes</label>
            <div class="mt-1 text-sm text-gray-900">{{ $auction->transaction->description }}</div>
        </div>
        @endif
    </div>
</div>
@endif
@endsection
