<?php

namespace App\Http\Controllers;

use App\Models\Status;
use Illuminate\Http\Request;
use App\Http\Requests\StatusStoreRequest;
use App\Http\Requests\StatusUpdateRequest;

class StatusController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Status::class);

        $statuses = Status::search($request->search)
            ->latest()
            ->get();

        return view('app.statuses.index', compact('statuses'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Status::class);

        return view('app.statuses.create');
    }

    /**
     * @param \App\Http\Requests\StatusStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StatusStoreRequest $request)
    {
        $this->authorize('create', Status::class);

        $validated = $request->validated();

        $status = Status::create($validated);

        return redirect()
            ->route('statuses.edit', $status)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Status $status)
    {
        $this->authorize('view', $status);

        return view('app.statuses.show', compact('status'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Status $status)
    {
        $this->authorize('update', $status);

        return view('app.statuses.edit', compact('status'));
    }

    /**
     * @param \App\Http\Requests\StatusUpdateRequest $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function update(StatusUpdateRequest $request, Status $status)
    {
        $this->authorize('update', $status);

        $validated = $request->validated();

        $status->update($validated);

        return redirect()
            ->route('statuses.edit', $status)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Status $status
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Status $status)
    {
        $this->authorize('delete', $status);

        $status->delete();

        return redirect()
            ->route('statuses.index')
            ->withSuccess(__('crud.common.removed'));
    }

    // Modernized Admin Methods

    /**
     * Modernized admin index view for statuses
     */
    public function modernizedIndex(Request $request)
    {
        $this->authorize('view-any', Status::class);

        $statuses = Status::search($request->search)
            ->latest()
            ->get();

        return view('admin.modernized-statuses.index', compact('statuses'));
    }

    /**
     * Modernized admin create view for statuses
     */
    public function modernizedCreate(Request $request)
    {
        $this->authorize('create', Status::class);

        return view('admin.modernized-statuses.create');
    }

    /**
     * Modernized admin store method for statuses
     */
    public function modernizedStore(StatusStoreRequest $request)
    {
        $this->authorize('create', Status::class);

        $validated = $request->validated();

        $status = Status::create($validated);

        return redirect()
            ->route('admin.modernized.statuses.edit', $status)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Modernized admin show view for statuses
     */
    public function modernizedShow(Request $request, Status $status)
    {
        $this->authorize('view', $status);

        return view('admin.modernized-statuses.show', compact('status'));
    }

    /**
     * Modernized admin edit view for statuses
     */
    public function modernizedEdit(Request $request, Status $status)
    {
        $this->authorize('update', $status);

        return view('admin.modernized-statuses.edit', compact('status'));
    }

    /**
     * Modernized admin update method for statuses
     */
    public function modernizedUpdate(StatusUpdateRequest $request, Status $status)
    {
        $this->authorize('update', $status);

        $validated = $request->validated();

        $status->update($validated);

        return redirect()
            ->route('admin.modernized.statuses.edit', $status)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * Modernized admin destroy method for statuses
     */
    public function modernizedDestroy(Request $request, Status $status)
    {
        $this->authorize('delete', $status);

        $status->delete();

        return redirect()
            ->route('admin.modernized.statuses.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
